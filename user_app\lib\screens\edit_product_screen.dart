import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../constants/app_constants.dart';
import '../models/product_model.dart';
import '../models/category_model.dart';
import '../services/product_service.dart';
import '../services/category_service.dart';
import '../services/image_service.dart';
import '../widgets/product_dashboard/product_form_widgets.dart';
import '../utils/role_access_control.dart';
import '../providers/auth_provider.dart';
import 'package:provider/provider.dart';

class EditProductScreen extends StatefulWidget {
  final ProductModel product;

  const EditProductScreen({
    super.key,
    required this.product,
  });

  @override
  State<EditProductScreen> createState() => _EditProductScreenState();
}

class _EditProductScreenState extends State<EditProductScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  late TextEditingController _stockController;


  late String _selectedCategory;
  List<XFile> _newImages = [];
  List<String> _existingImageUrls = [];
  late List<String> _tags;
  late bool _isAvailable;
  bool _isLoading = false;

  List<CategoryModel> _categories = [];
  bool _isLoadingCategories = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadCategories();
  }

  void _initializeControllers() {
    _nameController = TextEditingController(text: widget.product.name);
    _descriptionController = TextEditingController(text: widget.product.description);
    _stockController = TextEditingController(text: widget.product.stockQuantity.toString());

    _selectedCategory = widget.product.category;
    _existingImageUrls = List.from(widget.product.imageUrls);
    _tags = List.from(widget.product.tags);
    _isAvailable = widget.product.isAvailable;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _stockController.dispose();

    super.dispose();
  }

  Future<void> _loadCategories() async {
    setState(() {
      _isLoadingCategories = true;
    });

    try {
      final categories = await CategoryService.getAllActiveCategories();
      if (mounted) {
        setState(() {
          _categories = categories;
          _isLoadingCategories = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingCategories = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load categories: $e')),
        );
      }
    }
  }

  Future<void> _pickImages() async {
    try {
      final totalImages = _existingImageUrls.length + _newImages.length;
      final remainingSlots = 5 - totalImages;

      if (remainingSlots <= 0) {
        _showErrorSnackBar('Maximum 5 images allowed');
        return;
      }

      final images = await ImageService.pickMultipleImages(maxImages: remainingSlots);
      if (images != null && images.isNotEmpty) {
        setState(() {
          _newImages.addAll(images);
        });
      }
    } catch (e) {
      _showErrorSnackBar('Error selecting images: $e');
    }
  }

  void _removeNewImage(int index) {
    setState(() {
      _newImages.removeAt(index);
    });
  }

  void _removeExistingImage(int index) {
    setState(() {
      _existingImageUrls.removeAt(index);
    });
  }

  void _addTag(String tag) {
    if (tag.isNotEmpty && !_tags.contains(tag.toLowerCase())) {
      setState(() {
        _tags.add(tag.toLowerCase());
      });
    }
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
    });
  }

  Future<void> _updateProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final totalImages = _existingImageUrls.length + _newImages.length;
    if (totalImages == 0) {
      _showErrorSnackBar('Please add at least one product image');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      List<String> finalImageUrls = List.from(_existingImageUrls);

      // Upload new images if any
      if (_newImages.isNotEmpty) {
        final newImageUrls = await ImageService.uploadProductImages(
          imageFiles: _newImages,
          productId: widget.product.id,
        );
        finalImageUrls.addAll(newImageUrls);
      }

      if (finalImageUrls.isEmpty) {
        throw Exception('No images available for the product');
      }

      // Create updated product model
      final updatedProduct = widget.product.copyWith(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        price: widget.product.price, // Keep existing price, admin will set it
        originalPrice: widget.product.originalPrice, // Keep existing original price
        category: _selectedCategory,
        imageUrls: finalImageUrls,
        tags: _tags,
        stockQuantity: _stockController.text.trim().isNotEmpty
            ? int.parse(_stockController.text)
            : 999999, // Large number for unlimited stock
        isAvailable: _isAvailable,
        updatedAt: DateTime.now(),
      );

      // Update in Firestore
      final success = await ProductService.updateProduct(updatedProduct);

      if (success) {
        if (mounted) {
          Navigator.of(context).pop(updatedProduct);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Product updated successfully!'),
              backgroundColor: AppConstants.successColor,
            ),
          );
        }
      } else {
        throw Exception('Failed to update product');
      }
    } catch (e) {
      _showErrorSnackBar('Error updating product: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final currentUser = authProvider.currentUser;

    // Check if user can upload/edit products
    if (!RoleAccessControl.canUploadProducts(currentUser)) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Access Denied'),
          backgroundColor: AppConstants.surfaceColor,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: AppConstants.textPrimaryColor),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.lock_outline,
                  size: 64,
                  color: AppConstants.textSecondaryColor,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Edit Restricted',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w600,
                    color: AppConstants.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  RoleAccessControl.getUnauthorizedMessage('upload_product'),
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 16,
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text(
          'Edit Product',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: AppConstants.textPrimaryColor,
          ),
        ),
        backgroundColor: AppConstants.surfaceColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppConstants.textPrimaryColor),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _updateProduct,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: AppConstants.primaryColor,
                    ),
                  )
                : const Text(
                    'Update',
                    style: TextStyle(
                      color: AppConstants.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.fromLTRB(
            AppConstants.paddingMedium,
            AppConstants.paddingMedium,
            AppConstants.paddingMedium,
            100, // Bottom padding for navigation gap
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Enhanced Header Section
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppConstants.paddingLarge + 4),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppConstants.primaryColor.withOpacity(0.9),
                      AppConstants.primaryColor.withOpacity(0.75),
                      AppConstants.primaryColor.withOpacity(0.6),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    stops: const [0.0, 0.6, 1.0],
                  ),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge + 4),
                  boxShadow: [
                    BoxShadow(
                      color: AppConstants.primaryColor.withOpacity(0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                      spreadRadius: 0,
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 2,
                        ),
                      ),
                      child: const Icon(
                        Icons.edit_outlined,
                        size: 40,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: AppConstants.paddingMedium + 4),
                    const Text(
                      'Edit Product',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.w800,
                        color: Colors.white,
                        letterSpacing: -0.5,
                      ),
                    ),
                    const SizedBox(height: AppConstants.paddingSmall + 2),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppConstants.paddingMedium,
                        vertical: AppConstants.paddingSmall,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: const Text(
                        'Update your product information and settings',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                          height: 1.3,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Product Images Section
              _buildSectionCard(
                title: 'Product Images',
                subtitle: 'Update your product photos',
                icon: Icons.photo_library,
                child: EditProductImagePicker(
                  existingImages: _existingImageUrls,
                  newImages: _newImages,
                  onPickImages: _pickImages,
                  onRemoveNewImage: _removeNewImage,
                  onRemoveExistingImage: _removeExistingImage,
                ),
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Basic Information
              _buildSectionCard(
                title: 'Basic Information',
                subtitle: 'Update product details',
                icon: Icons.info_outline,
                child: Column(
                  children: [
                    ProductFormField(
                      controller: _nameController,
                      label: 'Product Name',
                      hint: 'Enter product name',
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Product name is required';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: AppConstants.paddingMedium),

                    ProductFormField(
                      controller: _descriptionController,
                      label: 'Description',
                      hint: 'Describe your product',
                      maxLines: 4,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Description is required';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: AppConstants.paddingMedium),

                    _isLoadingCategories
                        ? const Center(child: CircularProgressIndicator())
                        : DatabaseCategoryDropdown(
                            value: _selectedCategory,
                            categories: _categories,
                            onChanged: (value) {
                              setState(() {
                                _selectedCategory = value!;
                              });
                            },
                          ),
                  ],
                ),
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Stock Section
              _buildSectionCard(
                title: 'Stock Information',
                subtitle: 'Manage product inventory',
                icon: Icons.inventory,
                child: ProductFormField(
                  controller: _stockController,
                  label: 'Stock Quantity (Optional)',
                  hint: 'Enter stock quantity (leave empty for unlimited)',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value != null && value.trim().isNotEmpty) {
                      if (int.tryParse(value) == null) {
                        return 'Enter valid quantity';
                      }
                    }
                    return null;
                  },
                ),
              ),

              // Tags Section
              _buildSectionCard(
                title: 'Tags',
                subtitle: 'Add keywords to help buyers find your product',
                icon: Icons.local_offer,
                child: TagInput(
                  tags: _tags,
                  onAddTag: _addTag,
                  onRemoveTag: _removeTag,
                ),
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Settings Section
              _buildSectionCard(
                title: 'Product Settings',
                subtitle: 'Configure product visibility',
                icon: Icons.settings,
                child: Container(
                  padding: const EdgeInsets.all(AppConstants.paddingMedium),
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor.withOpacity(0.03),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                    border: Border.all(
                      color: AppConstants.primaryColor.withOpacity(0.1),
                      width: 1,
                    ),
                  ),
                  child: SwitchListTile(
                    title: const Text(
                      'Available for Sale',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: AppConstants.fontSizeMedium,
                      ),
                    ),
                    subtitle: const Text(
                      'Make this product visible to buyers',
                      style: TextStyle(
                        fontSize: AppConstants.fontSizeSmall,
                      ),
                    ),
                    value: _isAvailable,
                    onChanged: (value) {
                      setState(() {
                        _isAvailable = value;
                      });
                    },
                    activeColor: AppConstants.primaryColor,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ),

              const SizedBox(height: AppConstants.paddingXLarge),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppConstants.surfaceColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge + 4),
        border: Border.all(
          color: AppConstants.primaryColor.withOpacity(0.08),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryColor.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge + 4),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Enhanced header with gradient background
            Container(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppConstants.primaryColor.withOpacity(0.08),
                    AppConstants.primaryColor.withOpacity(0.03),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                border: Border.all(
                  color: AppConstants.primaryColor.withOpacity(0.1),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppConstants.primaryColor,
                          AppConstants.primaryColor.withOpacity(0.8),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: AppConstants.primaryColor.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Icon(
                      icon,
                      color: Colors.white,
                      size: 22,
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium + 2),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeLarge + 1,
                            fontWeight: FontWeight.w700,
                            color: AppConstants.textPrimaryColor,
                            letterSpacing: -0.5,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          subtitle,
                          style: TextStyle(
                            fontSize: AppConstants.fontSizeSmall + 1,
                            color: AppConstants.textSecondaryColor,
                            fontWeight: FontWeight.w500,
                            height: 1.3,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Optional status indicator
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppConstants.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.edit_outlined,
                      color: AppConstants.primaryColor,
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: AppConstants.paddingLarge + 4),
            child,
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: AppConstants.fontSizeLarge,
        fontWeight: FontWeight.w600,
        color: AppConstants.textPrimaryColor,
      ),
    );
  }
}

class DatabaseCategoryDropdown extends StatelessWidget {
  final String value;
  final List<CategoryModel> categories;
  final ValueChanged<String?> onChanged;

  const DatabaseCategoryDropdown({
    super.key,
    required this.value,
    required this.categories,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        border: Border.all(color: AppConstants.borderColor),
      ),
      child: DropdownButtonFormField<String>(
        value: categories.any((cat) => cat.name == value) ? value : null,
        decoration: const InputDecoration(
          labelText: 'Category',
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: AppConstants.paddingMedium,
            vertical: AppConstants.paddingSmall,
          ),
        ),
        items: categories.map((category) {
          return DropdownMenuItem<String>(
            value: category.name,
            child: Row(
              children: [
                if (category.iconName != null)
                  Icon(
                    _getIconData(category.iconName!),
                    size: 20,
                    color: AppConstants.primaryColor,
                  ),
                if (category.iconName != null) const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    category.name,
                    style: const TextStyle(fontSize: AppConstants.fontSizeMedium),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
        onChanged: onChanged,
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'Please select a category';
          }
          return null;
        },
      ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'phone_android':
        return Icons.phone_android;
      case 'checkroom':
        return Icons.checkroom;
      case 'chair':
        return Icons.chair;
      case 'sports_soccer':
        return Icons.sports_soccer;
      case 'book':
        return Icons.book;
      case 'face':
        return Icons.face;
      case 'directions_car':
        return Icons.directions_car;
      case 'restaurant':
        return Icons.restaurant;
      case 'health_and_safety':
        return Icons.health_and_safety;
      case 'home':
        return Icons.home;
      case 'toys':
        return Icons.toys;
      default:
        return Icons.category;
    }
  }
}

class EditProductImagePicker extends StatelessWidget {
  final List<String> existingImages;
  final List<XFile> newImages;
  final VoidCallback onPickImages;
  final Function(int) onRemoveNewImage;
  final Function(int) onRemoveExistingImage;

  const EditProductImagePicker({
    super.key,
    required this.existingImages,
    required this.newImages,
    required this.onPickImages,
    required this.onRemoveNewImage,
    required this.onRemoveExistingImage,
  });

  @override
  Widget build(BuildContext context) {
    final totalImages = existingImages.length + newImages.length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Product Images',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.w600,
                color: AppConstants.textPrimaryColor,
              ),
            ),
            Text(
              '$totalImages/5',
              style: const TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                color: AppConstants.textSecondaryColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingMedium),

        // Image Grid
        if (totalImages > 0)
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 1,
            ),
            itemCount: totalImages + (totalImages < 5 ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == totalImages) {
                return _buildAddImageButton();
              }

              if (index < existingImages.length) {
                return _buildExistingImageItem(existingImages[index], index);
              } else {
                final newImageIndex = index - existingImages.length;
                return _buildNewImageItem(newImages[newImageIndex], newImageIndex);
              }
            },
          )
        else
          _buildAddImageButton(),
      ],
    );
  }

  Widget _buildExistingImageItem(String imageUrl, int index) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            image: DecorationImage(
              image: NetworkImage(imageUrl),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
            onTap: () => onRemoveExistingImage(index),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                color: AppConstants.errorColor,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                color: Colors.white,
                size: 16,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNewImageItem(XFile image, int index) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            border: Border.all(
              color: AppConstants.primaryColor.withOpacity(0.5),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
            child: _buildImageWidget(image),
          ),
        ),
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
            onTap: () => onRemoveNewImage(index),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: AppConstants.errorColor,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.close,
                color: Colors.white,
                size: 16,
              ),
            ),
          ),
        ),
        // New image indicator
        Positioned(
          bottom: 4,
          left: 4,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: AppConstants.primaryColor,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 4,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: const Text(
              'NEW',
              style: TextStyle(
                color: Colors.white,
                fontSize: 8,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAddImageButton() {
    return GestureDetector(
      onTap: onPickImages,
      child: Container(
        decoration: BoxDecoration(
          color: AppConstants.backgroundColor,
          borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          border: Border.all(
            color: AppConstants.primaryColor.withOpacity(0.3),
            width: 2,
            style: BorderStyle.solid,
          ),
        ),
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_photo_alternate_outlined,
              color: AppConstants.primaryColor,
              size: 32,
            ),
            SizedBox(height: 4),
            Text(
              'Add Image',
              style: TextStyle(
                color: AppConstants.primaryColor,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageWidget(XFile image) {
    if (kIsWeb) {
      // For web, use FutureBuilder with readAsBytes
      return FutureBuilder<Uint8List>(
        future: image.readAsBytes(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return Container(
              color: AppConstants.backgroundColor,
              child: const Center(
                child: CircularProgressIndicator(
                  color: AppConstants.primaryColor,
                  strokeWidth: 2,
                ),
              ),
            );
          }

          if (snapshot.hasError || !snapshot.hasData) {
            return Container(
              color: AppConstants.backgroundColor,
              child: const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.broken_image,
                    color: AppConstants.errorColor,
                    size: 32,
                  ),
                  SizedBox(height: 4),
                  Text(
                    'Load Error',
                    style: TextStyle(
                      color: AppConstants.errorColor,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            );
          }

          return Image.memory(
            snapshot.data!,
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
          );
        },
      );
    } else {
      // For mobile, use Image.file
      return Image.file(
        File(image.path),
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: AppConstants.backgroundColor,
            child: const Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.broken_image,
                  color: AppConstants.errorColor,
                  size: 32,
                ),
                SizedBox(height: 4),
                Text(
                  'Load Error',
                  style: TextStyle(
                    color: AppConstants.errorColor,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          );
        },
      );
    }
  }
}