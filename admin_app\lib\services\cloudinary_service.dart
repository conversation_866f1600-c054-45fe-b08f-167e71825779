import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class CloudinaryService {
  // Cloudinary configuration
  static const String _cloudName = 'doxd3bscf';
  static const String _apiKey = '251519664785658';
  static const String _apiSecret = 'Y3-ZEUAZ5b121-gr9UBbzfV006E';
  static const String _uploadUrl = 'https://api.cloudinary.com/v1_1/$_cloudName/image/upload';
  static const String _deleteUrl = 'https://api.cloudinary.com/v1_1/$_cloudName/image/destroy';

  /// Generate signature for Cloudinary upload
  static String _generateSignature({
    required Map<String, String> params,
    required String apiSecret,
  }) {
    // Sort parameters alphabetically
    final sortedParams = Map.fromEntries(
      params.entries.toList()..sort((a, b) => a.key.compareTo(b.key)),
    );

    // Create query string
    final queryString = sortedParams.entries
        .map((entry) => '${entry.key}=${entry.value}')
        .join('&');

    // Add API secret
    final stringToSign = '$queryString$apiSecret';

    // Generate SHA1 hash
    final bytes = utf8.encode(stringToSign);
    final digest = sha1.convert(bytes);

    return digest.toString();
  }

  /// Upload single image to Cloudinary
  static Future<String?> uploadImage({
    required dynamic imageFile,
    required String folder,
    String? publicId,
    int quality = 85,
    int? width,
    int? height,
  }) async {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      
      // Prepare parameters
      final params = <String, String>{
        'timestamp': timestamp,
        'folder': folder,
        'quality': quality.toString(),
      };

      if (publicId != null) {
        params['public_id'] = publicId;
      }

      if (width != null) {
        params['width'] = width.toString();
      }

      if (height != null) {
        params['height'] = height.toString();
      }

      // Generate signature
      final signature = _generateSignature(params: params, apiSecret: _apiSecret);

      // Prepare multipart request
      final request = http.MultipartRequest('POST', Uri.parse(_uploadUrl));
      
      // Add parameters
      params.forEach((key, value) {
        request.fields[key] = value;
      });
      
      request.fields['api_key'] = _apiKey;
      request.fields['signature'] = signature;

      // Add file
      if (kIsWeb) {
        // For web platform
        Uint8List bytes;
        if (imageFile is Uint8List) {
          bytes = imageFile;
        } else {
          throw Exception('Invalid image file type for web platform');
        }
        
        request.files.add(http.MultipartFile.fromBytes(
          'file',
          bytes,
          filename: 'image.jpg',
        ));
      } else {
        // For mobile platforms
        File file;
        if (imageFile is File) {
          file = imageFile;
        } else {
          throw Exception('Invalid image file type for mobile platform');
        }
        
        request.files.add(await http.MultipartFile.fromPath('file', file.path));
      }

      // Send request
      final response = await request.send();
      final responseBody = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(responseBody);
        return jsonResponse['secure_url'] as String?;
      } else {
        print('Cloudinary upload failed: ${response.statusCode} - $responseBody');
        return null;
      }
    } catch (e) {
      print('Error uploading image to Cloudinary: $e');
      return null;
    }
  }

  /// Upload multiple images to Cloudinary
  static Future<List<String>> uploadMultipleImages({
    required List<dynamic> imageFiles,
    required String folder,
    String? basePublicId,
    int quality = 85,
    int? width,
    int? height,
  }) async {
    final uploadedUrls = <String>[];

    for (int i = 0; i < imageFiles.length; i++) {
      final publicId = basePublicId != null ? '${basePublicId}_$i' : null;
      
      final url = await uploadImage(
        imageFile: imageFiles[i],
        folder: folder,
        publicId: publicId,
        quality: quality,
        width: width,
        height: height,
      );

      if (url != null) {
        uploadedUrls.add(url);
      }
    }

    return uploadedUrls;
  }

  /// Upload product image with optimized settings
  static Future<String?> uploadProductImage({
    required dynamic imageFile,
    required String productId,
    int? index,
  }) async {
    final publicId = index != null 
        ? 'product_${productId}_$index' 
        : 'product_$productId';
        
    return await uploadImage(
      imageFile: imageFile,
      folder: 'product_images',
      publicId: publicId,
      quality: 85,
      width: 800,
      height: 800,
    );
  }

  /// Upload profile image with optimized settings
  static Future<String?> uploadProfileImage({
    required dynamic imageFile,
    required String userId,
  }) async {
    return await uploadImage(
      imageFile: imageFile,
      folder: 'profile_images',
      publicId: 'profile_$userId',
      quality: 90,
      width: 400,
      height: 400,
    );
  }

  /// Delete image from Cloudinary
  static Future<bool> deleteImage(String publicId) async {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      
      final params = <String, String>{
        'public_id': publicId,
        'timestamp': timestamp,
      };

      final signature = _generateSignature(params: params, apiSecret: _apiSecret);

      final response = await http.post(
        Uri.parse(_deleteUrl),
        body: {
          ...params,
          'api_key': _apiKey,
          'signature': signature,
        },
      );

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return jsonResponse['result'] == 'ok';
      } else {
        print('Cloudinary delete failed: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      print('Error deleting image from Cloudinary: $e');
      return false;
    }
  }

  /// Get optimized image URL with transformations
  static String getOptimizedImageUrl({
    required String imageUrl,
    int? width,
    int? height,
    int quality = 80,
    String format = 'auto',
    String crop = 'fill',
  }) {
    if (!imageUrl.contains('cloudinary.com')) {
      return imageUrl; // Return original URL if not a Cloudinary URL
    }

    try {
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments.toList();
      
      // Find the upload segment
      final uploadIndex = pathSegments.indexOf('upload');
      if (uploadIndex == -1) return imageUrl;

      // Build transformation string
      final transformations = <String>[];
      
      if (width != null) transformations.add('w_$width');
      if (height != null) transformations.add('h_$height');
      transformations.add('q_$quality');
      transformations.add('f_$format');
      transformations.add('c_$crop');

      final transformationString = transformations.join(',');

      // Insert transformation after upload
      pathSegments.insert(uploadIndex + 1, transformationString);

      // Rebuild URL
      final newUri = uri.replace(pathSegments: pathSegments);
      return newUri.toString();
    } catch (e) {
      print('Error optimizing image URL: $e');
      return imageUrl;
    }
  }

  /// Extract public ID from Cloudinary URL
  static String? extractPublicId(String imageUrl) {
    try {
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments;
      
      // Find the upload segment
      final uploadIndex = pathSegments.indexOf('upload');
      if (uploadIndex == -1) return null;

      // Get segments after upload (skip transformation if present)
      final relevantSegments = pathSegments.skip(uploadIndex + 1).toList();
      
      // Remove transformation segment if present
      if (relevantSegments.isNotEmpty && relevantSegments.first.contains('_')) {
        relevantSegments.removeAt(0);
      }

      // Join remaining segments and remove file extension
      final publicIdWithExtension = relevantSegments.join('/');
      final lastDotIndex = publicIdWithExtension.lastIndexOf('.');
      
      if (lastDotIndex != -1) {
        return publicIdWithExtension.substring(0, lastDotIndex);
      }
      
      return publicIdWithExtension;
    } catch (e) {
      print('Error extracting public ID: $e');
      return null;
    }
  }

  /// Get image info from Cloudinary
  static Future<Map<String, dynamic>?> getImageInfo(String publicId) async {
    try {
      final url = 'https://api.cloudinary.com/v1_1/$_cloudName/resources/image/upload/$publicId';
      
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final params = <String, String>{
        'timestamp': timestamp,
      };

      final signature = _generateSignature(params: params, apiSecret: _apiSecret);

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Authorization': 'Basic ${base64Encode(utf8.encode('$_apiKey:$_apiSecret'))}',
        },
      );

      if (response.statusCode == 200) {
        return json.decode(response.body) as Map<String, dynamic>;
      } else {
        print('Failed to get image info: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      print('Error getting image info: $e');
      return null;
    }
  }
}
